#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动登录网站后台程序
网址：https://query.ekao888.com/admin/login.php
功能：自动识别验证码并登录
"""

import time
import getpass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from PIL import Image
import pytesseract
import requests
from io import BytesIO
import base64


class AutoLogin:
    def __init__(self):
        self.driver = None
        self.login_url = "https://query.ekao888.com/admin/login.php"
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 可选：无头模式（不显示浏览器窗口）
        # chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            print("✅ Chrome浏览器启动成功")
        except Exception as e:
            print(f"❌ Chrome浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return False
        return True
    
    def get_user_credentials(self):
        """获取用户输入的用户名和密码"""
        print("\n=== 请输入登录信息 ===")
        username = input("用户名: ").strip()
        password = getpass.getpass("密码: ").strip()
        
        if not username or not password:
            print("❌ 用户名和密码不能为空")
            return None, None
            
        return username, password
    
    def capture_captcha(self):
        """截取验证码图片并进行OCR识别"""
        try:
            # 等待验证码图片加载
            captcha_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//img[contains(@src, 'captcha') or contains(@src, 'code') or contains(@alt, '验证码')]"))
            )
            
            # 获取验证码图片
            captcha_screenshot = captcha_element.screenshot_as_png
            captcha_image = Image.open(BytesIO(captcha_screenshot))
            
            # 图片预处理（提高OCR识别率）
            captcha_image = captcha_image.convert('L')  # 转为灰度图
            captcha_image = captcha_image.resize((captcha_image.width * 3, captcha_image.height * 3))  # 放大图片
            
            # 使用pytesseract进行OCR识别
            # 配置OCR参数，只识别数字和字母
            custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
            captcha_text = pytesseract.image_to_string(captcha_image, config=custom_config).strip()
            
            print(f"🔍 识别到的验证码: {captcha_text}")
            return captcha_text
            
        except Exception as e:
            print(f"❌ 验证码识别失败: {e}")
            return None
    
    def fill_login_form(self, username, password, captcha):
        """填写登录表单"""
        try:
            # 填写用户名
            username_field = self.driver.find_element(By.NAME, "username")
            username_field.clear()
            username_field.send_keys(username)
            print("✅ 用户名填写完成")
            
            # 填写密码
            password_field = self.driver.find_element(By.NAME, "password")
            password_field.clear()
            password_field.send_keys(password)
            print("✅ 密码填写完成")
            
            # 填写验证码
            captcha_field = self.driver.find_element(By.NAME, "captcha")
            captcha_field.clear()
            captcha_field.send_keys(captcha)
            print("✅ 验证码填写完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 表单填写失败: {e}")
            return False
    
    def click_login_button(self):
        """点击登录按钮"""
        try:
            login_button = self.driver.find_element(By.XPATH, "//input[@type='submit' and @value='登录']")
            login_button.click()
            print("✅ 登录按钮点击成功")
            return True
        except Exception as e:
            print(f"❌ 登录按钮点击失败: {e}")
            return False
    
    def check_login_result(self):
        """检查登录结果"""
        try:
            time.sleep(3)  # 等待页面加载
            
            # 检查是否还在登录页面（登录失败）
            if "login.php" in self.driver.current_url:
                print("❌ 登录失败，仍在登录页面")
                return False
            else:
                print(f"✅ 登录成功！当前页面: {self.driver.current_url}")
                return True
                
        except Exception as e:
            print(f"❌ 登录结果检查失败: {e}")
            return False
    
    def login(self):
        """主登录流程"""
        print("🚀 开始自动登录流程...")
        
        # 1. 设置浏览器驱动
        if not self.setup_driver():
            return False
        
        try:
            # 2. 打开登录页面
            print(f"📖 正在打开登录页面: {self.login_url}")
            self.driver.get(self.login_url)
            time.sleep(2)
            
            # 3. 获取用户凭据
            username, password = self.get_user_credentials()
            if not username or not password:
                return False
            
            # 4. 识别验证码
            captcha = self.capture_captcha()
            if not captcha:
                print("❌ 验证码识别失败，请手动处理")
                input("请手动输入验证码后按回车继续...")
                return False
            
            # 5. 填写登录表单
            if not self.fill_login_form(username, password, captcha):
                return False
            
            # 6. 点击登录按钮
            if not self.click_login_button():
                return False
            
            # 7. 检查登录结果
            if self.check_login_result():
                print("🎉 自动登录成功！")
                input("按回车键关闭浏览器...")
                return True
            else:
                print("❌ 登录失败，可能是验证码错误或凭据无效")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程中发生错误: {e}")
            return False
        
        finally:
            if self.driver:
                self.driver.quit()
                print("🔒 浏览器已关闭")


def main():
    """主函数"""
    print("=" * 50)
    print("🤖 自动登录网站后台程序")
    print("📍 目标网站: https://query.ekao888.com/admin/login.php")
    print("=" * 50)
    
    auto_login = AutoLogin()
    success = auto_login.login()
    
    if success:
        print("\n✅ 程序执行完成！")
    else:
        print("\n❌ 程序执行失败！")


if __name__ == "__main__":
    main()
