#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版自动登录程序
当OCR识别有问题时，可以手动输入验证码
"""

import time
import getpass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options


def auto_login_simple():
    """简化版自动登录"""
    login_url = "https://query.ekao888.com/admin/login.php"
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = None
    
    try:
        # 启动浏览器
        print("🚀 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.maximize_window()
        
        # 打开登录页面
        print(f"📖 打开登录页面: {login_url}")
        driver.get(login_url)
        time.sleep(2)
        
        # 获取用户输入
        print("\n=== 请输入登录信息 ===")
        username = input("用户名: ").strip()
        password = getpass.getpass("密码: ").strip()
        
        if not username or not password:
            print("❌ 用户名和密码不能为空")
            return False
        
        # 填写用户名
        username_field = driver.find_element(By.NAME, "username")
        username_field.clear()
        username_field.send_keys(username)
        print("✅ 用户名填写完成")
        
        # 填写密码
        password_field = driver.find_element(By.NAME, "password")
        password_field.clear()
        password_field.send_keys(password)
        print("✅ 密码填写完成")
        
        # 手动输入验证码
        print("\n👀 请查看浏览器中的验证码图片")
        captcha = input("请输入验证码: ").strip()
        
        if not captcha:
            print("❌ 验证码不能为空")
            return False
        
        # 填写验证码
        captcha_field = driver.find_element(By.NAME, "captcha")
        captcha_field.clear()
        captcha_field.send_keys(captcha)
        print("✅ 验证码填写完成")
        
        # 点击登录按钮
        login_button = driver.find_element(By.XPATH, "//input[@type='submit' and @value='登录']")
        login_button.click()
        print("✅ 登录按钮点击成功")
        
        # 等待页面加载并检查结果
        time.sleep(3)
        
        if "login.php" in driver.current_url:
            print("❌ 登录失败，请检查用户名、密码或验证码")
            return False
        else:
            print(f"✅ 登录成功！当前页面: {driver.current_url}")
            print("🎉 自动登录完成！")
            input("按回车键关闭浏览器...")
            return True
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()
            print("🔒 浏览器已关闭")


if __name__ == "__main__":
    print("=" * 50)
    print("🤖 简化版自动登录程序")
    print("📍 目标网站: https://query.ekao888.com/admin/login.php")
    print("💡 验证码需要手动输入")
    print("=" * 50)
    
    success = auto_login_simple()
    
    if success:
        print("\n✅ 程序执行完成！")
    else:
        print("\n❌ 程序执行失败！")
