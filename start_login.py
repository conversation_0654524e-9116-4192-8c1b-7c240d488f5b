#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
启动器 - 避免ServBay Python别名问题
"""

import subprocess
import sys
import os

def main():
    print("🤖 自动登录程序启动器")
    print("=" * 50)
    
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 使用系统原生Python路径
    python_path = "/usr/bin/python3"
    
    print("请选择运行模式：")
    print("1. 简化版 (手动输入验证码) - 推荐")
    print("2. 完整版 (自动识别验证码)")
    print("3. 快速测试")
    print("")
    
    try:
        choice = input("请输入选择 (1, 2, 或 3): ").strip()
    except KeyboardInterrupt:
        print("\n\n👋 程序已取消")
        return
    
    if choice == "1":
        script_name = "simple_login.py"
        print("🚀 启动简化版程序...")
    elif choice == "2":
        script_name = "auto_login.py"
        print("🚀 启动完整版程序...")
    elif choice == "3":
        script_name = "test_login.py"
        print("🧪 启动测试程序...")
    else:
        script_name = "simple_login.py"
        print("🚀 默认启动简化版程序...")
    
    script_path = os.path.join(script_dir, script_name)
    
    try:
        # 使用subprocess运行脚本
        result = subprocess.run([python_path, script_path], 
                              cwd=script_dir,
                              check=False)
        
        if result.returncode == 0:
            print("\n✅ 程序执行完成！")
        else:
            print(f"\n❌ 程序执行失败，退出码: {result.returncode}")
            
    except FileNotFoundError:
        print(f"❌ 未找到Python解释器: {python_path}")
        print("请检查Python安装")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
