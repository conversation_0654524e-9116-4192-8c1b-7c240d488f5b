# 🤖 自动登录网站后台程序

这是一个用于自动登录 `https://query.ekao888.com/admin/login.php` 的Python程序。

## ✨ 功能特点

- 🔐 自动填写用户名和密码（运行时输入）
- 🔍 自动识别验证码（OCR技术）
- 🖱️ 自动点击登录按钮
- ✅ 自动检测登录结果

## 📋 环境要求

### 1. Python环境
- Python 3.7 或更高版本

### 2. 系统依赖

#### Windows系统：
1. 安装Chrome浏览器
2. 下载ChromeDriver：https://chromedriver.chromium.org/
3. 将ChromeDriver.exe放到系统PATH中或项目目录下
4. 安装Tesseract OCR：https://github.com/UB-Mannheim/tesseract/wiki

#### macOS系统：
```bash
# 安装Chrome浏览器（从官网下载）
# 安装ChromeDriver
brew install chromedriver

# 安装Tesseract OCR
brew install tesseract
```

#### Linux系统：
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install google-chrome-stable
sudo apt-get install chromium-chromedriver
sudo apt-get install tesseract-ocr

# CentOS/RHEL
sudo yum install google-chrome-stable
sudo yum install chromedriver
sudo yum install tesseract
```

## 🚀 安装步骤

### 1. 克隆或下载项目文件

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 验证环境
确保以下命令能正常执行：
```bash
# 检查Chrome
google-chrome --version

# 检查ChromeDriver
chromedriver --version

# 检查Tesseract
tesseract --version
```

## 📖 使用方法

### 1. 运行程序
```bash
python auto_login.py
```

### 2. 按提示输入
- 输入用户名
- 输入密码（输入时不会显示，这是正常的安全特性）

### 3. 程序自动执行
- 自动打开浏览器
- 自动识别验证码
- 自动填写表单
- 自动点击登录
- 显示登录结果

## ⚙️ 配置选项

### 无头模式（后台运行）
如果你不想看到浏览器窗口，可以启用无头模式：

在 `auto_login.py` 文件中找到这行：
```python
# chrome_options.add_argument('--headless')
```

取消注释（删除#）：
```python
chrome_options.add_argument('--headless')
```

### 验证码识别优化
如果验证码识别率不高，可以尝试：

1. 调整OCR配置参数
2. 修改图片预处理方式
3. 手动输入验证码（程序会提示）

## 🔧 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   - 确保ChromeDriver版本与Chrome浏览器版本兼容
   - 下载对应版本：https://chromedriver.chromium.org/

2. **Tesseract未找到**
   - Windows：确保Tesseract安装路径在系统PATH中
   - 或在代码中指定路径：`pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'`

3. **验证码识别失败**
   - 程序会提示手动处理
   - 可以暂停程序，手动输入验证码

4. **网络连接问题**
   - 确保能正常访问目标网站
   - 检查防火墙设置

### 调试模式
如果遇到问题，可以启用详细日志：

在代码中添加：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## ⚠️ 注意事项

1. **合法使用**：请确保你有权限访问目标网站
2. **安全性**：不要在公共环境中运行，保护好你的凭据
3. **频率控制**：避免过于频繁的自动登录，以免被网站限制
4. **更新维护**：网站结构变化时可能需要更新代码

## 📝 许可证

本项目仅供学习和个人使用。

## 🤝 贡献

欢迎提交问题和改进建议！
