#!/bin/bash

echo "🤖 自动登录程序启动器"
echo "========================"

# 检查Python环境
if command -v /usr/bin/python3 &> /dev/null; then
    PYTHON_CMD="/usr/bin/python3"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 未找到Python环境"
    exit 1
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 选择运行模式
echo ""
echo "请选择运行模式："
echo "1. 简化版 (手动输入验证码)"
echo "2. 完整版 (自动识别验证码)"
echo ""
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo "🚀 启动简化版程序..."
        $PYTHON_CMD simple_login.py
        ;;
    2)
        echo "🚀 启动完整版程序..."
        $PYTHON_CMD auto_login.py
        ;;
    *)
        echo "❌ 无效选择，默认启动简化版..."
        $PYTHON_CMD simple_login.py
        ;;
esac

echo ""
echo "程序执行完毕！"
