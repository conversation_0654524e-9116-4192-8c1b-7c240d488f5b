#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
快速测试登录程序
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager


def quick_test():
    """快速测试浏览器和网站访问"""
    print("🧪 开始快速测试...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    driver = None
    
    try:
        # 启动浏览器
        print("🚀 启动浏览器...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 打开登录页面
        login_url = "https://query.ekao888.com/admin/login.php"
        print(f"📖 打开登录页面: {login_url}")
        driver.get(login_url)
        time.sleep(3)
        
        # 检查页面元素
        print("🔍 检查页面元素...")
        
        # 检查用户名输入框
        try:
            username_field = driver.find_element(By.NAME, "username")
            print("✅ 找到用户名输入框")
        except:
            print("❌ 未找到用户名输入框")
        
        # 检查密码输入框
        try:
            password_field = driver.find_element(By.NAME, "password")
            print("✅ 找到密码输入框")
        except:
            print("❌ 未找到密码输入框")
        
        # 检查验证码输入框
        try:
            captcha_field = driver.find_element(By.NAME, "captcha")
            print("✅ 找到验证码输入框")
        except:
            print("❌ 未找到验证码输入框")
        
        # 检查登录按钮
        try:
            login_button = driver.find_element(By.XPATH, "//input[@type='submit' and @value='登录']")
            print("✅ 找到登录按钮")
        except:
            print("❌ 未找到登录按钮")
        
        # 检查验证码图片
        try:
            captcha_img = driver.find_element(By.XPATH, "//img")
            print("✅ 找到验证码图片")
            print(f"   图片src: {captcha_img.get_attribute('src')}")
        except:
            print("❌ 未找到验证码图片")
        
        print("\n🎉 测试完成！浏览器将在5秒后关闭...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()
            print("🔒 浏览器已关闭")


if __name__ == "__main__":
    print("=" * 50)
    print("🧪 自动登录程序 - 快速测试")
    print("=" * 50)
    
    success = quick_test()
    
    if success:
        print("\n✅ 测试成功！环境配置正常")
        print("💡 现在可以运行完整的登录程序了")
    else:
        print("\n❌ 测试失败！请检查环境配置")
